version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: hbnb-mysql
    environment:
      MYSQL_ROOT_PASSWORD: rootpass
      MYSQL_DATABASE: hbnb_evo_2_db
      MYSQL_USER: hbnb_evo_2
      MYSQL_PASSWORD: hbnb_evo_2_pwd
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

volumes:
  mysql_data:
