#!/usr/bin/python3
""" Unittests for HBnB Evolution v2 Part 2 """

import unittest
from models.user import User

class TestUser(unittest.TestCase):
    """Test that the User model works as expected
    """

    def test_create_user(self):
        """Tests creation of User instances """
        user = User(first_name="<PERSON>", last_name="<PERSON>", email="<EMAIL>")

        assert user.first_name == "<PERSON>"
        assert user.last_name == "<PERSON>"
        assert user.email == "<EMAIL>"
        assert user.is_admin is False  # Default value
        print("User creation test passed!")

    # TODO: add more tests

if __name__ == '__main__':
    unittest.main()
